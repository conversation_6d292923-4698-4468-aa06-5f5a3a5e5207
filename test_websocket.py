#!/usr/bin/env python3

import asyncio
import websockets
import json
import requests

async def test_websocket():
    """Test the WebSocket endpoint after calling the analyze endpoint"""
    
    # First call the analyze endpoint to set up the file
    print("Step 1: Calling /api/analyze endpoint...")
    response = requests.post(
        "http://localhost:8001/api/analyze",
        json={"filepath": "/Users/<USER>/Downloads/Organized/Work_Projects/Biormika/B_JP_In.edf"}
    )
    
    if response.status_code == 200:
        print("✓ /api/analyze successful")
        print(f"Response: {response.json()}")
    else:
        print(f"✗ /api/analyze failed: {response.status_code} - {response.text}")
        return
    
    # Now test the WebSocket connection
    print("\nStep 2: Connecting to WebSocket...")
    try:
        async with websockets.connect("ws://localhost:8001/ws") as websocket:
            print("✓ WebSocket connected successfully")
            
            # Listen for messages
            message_count = 0
            max_messages = 10  # Limit to avoid infinite loop
            
            while message_count < max_messages:
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=30.0)
                    data = json.loads(message)
                    message_count += 1
                    
                    print(f"\nMessage {message_count}:")
                    print(f"Type: {data.get('type')}")
                    print(f"Message: {data.get('message', '')}")
                    
                    if data.get('type') == 'validation_error':
                        print(f"✗ Validation Error: {data.get('errors')}")
                        break
                    elif data.get('type') == 'validation_warning':
                        print(f"⚠ Validation Warning: {data.get('warnings')}")
                    elif data.get('type') == 'error':
                        print(f"✗ Error: {data.get('message')}")
                        break
                    elif data.get('type') == 'complete':
                        print("✓ Analysis completed successfully!")
                        break
                    elif data.get('type') == 'preview':
                        print("✓ Preview data received")
                    elif data.get('type') == 'chunk':
                        chunk_data = data.get('data', {})
                        progress = chunk_data.get('progress', 0)
                        print(f"✓ Chunk {chunk_data.get('chunk_number', 0)} processed ({progress:.1f}% complete)")
                        
                except asyncio.TimeoutError:
                    print("✗ Timeout waiting for message")
                    break
                except json.JSONDecodeError as e:
                    print(f"✗ Failed to parse JSON: {e}")
                    break
                    
    except Exception as e:
        print(f"✗ WebSocket connection failed: {e}")

if __name__ == "__main__":
    asyncio.run(test_websocket())
